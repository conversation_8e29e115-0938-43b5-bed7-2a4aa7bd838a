import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../models/trip_model.dart';

class SupabaseService {
  static final SupabaseClient _client = Supabase.instance.client;

  // Helper method to format phone number for Supabase Auth
  static String _formatPhoneForAuth(String phone) {
    // Remove all non-digit characters
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // Convert to international format with +212
    String formattedPhone = cleanPhone;
    if (formattedPhone.startsWith('0')) {
      // Remove leading 0 and add +212
      formattedPhone = '+212${formattedPhone.substring(1)}';
    } else if (formattedPhone.startsWith('212')) {
      // Add + if missing
      formattedPhone = '+$formattedPhone';
    } else if (!formattedPhone.startsWith('+212')) {
      // Add +212 prefix
      formattedPhone = '+212$formattedPhone';
    }

    return formattedPhone;
  }

  // Helper method to normalize phone for database storage
  static String _normalizePhone(String phone) {
    // Remove all non-digit characters
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // Convert to consistent format: remove leading +212 or 0
    String normalizedPhone = cleanPhone;
    if (normalizedPhone.startsWith('212')) {
      normalizedPhone = normalizedPhone.substring(3);
    } else if (normalizedPhone.startsWith('0')) {
      normalizedPhone = normalizedPhone.substring(1);
    }

    return normalizedPhone;
  }

  // Get current user from Supabase Auth
  static User? get currentUser => _client.auth.currentUser;

  // Get auth state changes stream
  static Stream<AuthState> get authStateChanges =>
      _client.auth.onAuthStateChange;

  // Authentication methods using Supabase Auth
  static Future<Map<String, dynamic>> signUpWithPhone({
    required String phone,
    required String password,
    required String fullName,
  }) async {
    try {
      final formattedPhone = _formatPhoneForAuth(phone);
      final normalizedPhone = _normalizePhone(phone);

      if (kDebugMode) {
        print('🔐 Attempting signup with phone: $formattedPhone');
      }

      // Sign up with Supabase Auth using phone
      final response = await _client.auth.signUp(
        phone: formattedPhone,
        password: password,
        data: {
          'full_name': fullName,
          'phone': normalizedPhone,
        },
      );

      if (response.user != null) {
        if (kDebugMode) {
          print('✅ User created successfully: ${response.user!.id}');
        }

        return {
          'success': true,
          'userId': response.user!.id,
          'message': 'تم إنشاء الحساب بنجاح',
        };
      } else {
        return {
          'success': false,
          'userId': null,
          'message': 'فشل في إنشاء الحساب',
        };
      }
    } on AuthException catch (e) {
      if (kDebugMode) {
        print('❌ Signup AuthException: $e');
      }

      String errorMessage;
      switch (e.message.toLowerCase()) {
        case 'user already registered':
          errorMessage = 'رقم الهاتف مسجل مسبقاً. يرجى تسجيل الدخول';
          break;
        case 'signup disabled':
          errorMessage = 'التسجيل غير متاح حالياً';
          break;
        default:
          errorMessage = 'فشل في إنشاء الحساب: ${e.message}';
      }

      return {
        'success': false,
        'userId': null,
        'message': errorMessage,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Signup general error: $e');
      }
      return {
        'success': false,
        'userId': null,
        'message': 'حدث خطأ غير متوقع. حاول مرة أخرى',
      };
    }
  }

  static Future<Map<String, dynamic>> signInWithPhone({
    required String phone,
    required String password,
  }) async {
    try {
      final formattedPhone = _formatPhoneForAuth(phone);

      if (kDebugMode) {
        print('🔐 Attempting login with phone: $formattedPhone');
      }

      // Sign in with Supabase Auth using phone
      final response = await _client.auth.signInWithPassword(
        phone: formattedPhone,
        password: password,
      );

      if (response.user != null) {
        if (kDebugMode) {
          print('✅ Login successful for user: ${response.user!.id}');
        }

        return {
          'success': true,
          'userId': response.user!.id,
          'message': 'تم تسجيل الدخول بنجاح',
        };
      } else {
        return {
          'success': false,
          'userId': null,
          'message': 'فشل في تسجيل الدخول',
        };
      }
    } on AuthException catch (e) {
      if (kDebugMode) {
        print('❌ Signin AuthException: $e');
      }

      String errorMessage;
      switch (e.message.toLowerCase()) {
        case 'invalid login credentials':
          errorMessage = 'رقم الهاتف أو كلمة المرور غير صحيحة';
          break;
        case 'email not confirmed':
          errorMessage = 'يجب تأكيد رقم الهاتف أولاً';
          break;
        case 'too many requests':
          errorMessage = 'محاولات كثيرة. حاول مرة أخرى لاحقاً';
          break;
        default:
          errorMessage = 'خطأ في تسجيل الدخول: ${e.message}';
      }

      return {
        'success': false,
        'userId': null,
        'message': errorMessage,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Signin general error: $e');
      }
      return {
        'success': false,
        'userId': null,
        'message': 'حدث خطأ غير متوقع. حاول مرة أخرى',
      };
    }
  }

  static Future<void> signOut() async {
    try {
      await _client.auth.signOut();

      if (kDebugMode) {
        print('✅ User signed out successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Signout error: $e');
      }
      rethrow;
    }
  }

  static Future<void> resetPassword(String email) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
    } catch (e) {
      if (kDebugMode) {
        print('Reset password error: $e');
      }
      rethrow;
    }
  }

  // User profile methods
  static Future<void> _createUserProfile({
    required String userId,
    required String phone,
    required String fullName,
    required String email,
  }) async {
    try {
      await _client.from('users').insert({
        'id': userId,
        'email': email,
        'full_name': fullName,
        'phone': phone,
        'role': 'traveler',
        'is_leader': false,
        'balance': 0.0,
        'is_verified': false,
        'rating': 0.0,
        'total_trips': 0,
        'total_ratings': 0,
        'badges': [],
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Create user profile error: $e');
      }
      rethrow;
    }
  }

  static Future<UserModel?> getUserProfile(String userId) async {
    try {
      final response =
          await _client.from('users').select().eq('id', userId).single();

      return UserModel.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('Get user profile error: $e');
      }
      return null;
    }
  }

  static Future<void> updateUserProfile(UserModel user) async {
    try {
      await _client.from('users').update(user.toJson()).eq('id', user.id);
    } catch (e) {
      if (kDebugMode) {
        print('Update user profile error: $e');
      }
      rethrow;
    }
  }

  // Trip methods
  static Future<String> createTrip(TripModel trip) async {
    try {
      // Get current authenticated user
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception(
            'User not authenticated. Please log in to create a trip.');
      }

      // Prepare trip data for insertion
      final tripData = trip.toJson();

      // Remove null id for insertion
      tripData.remove('id');

      // CRITICAL FIX: Ensure driver_id is set to current user's ID
      tripData['driver_id'] = user.id;
      tripData['leader_id'] = user.id; // Keep both for compatibility

      // Ensure required fields have default values
      tripData['status'] = tripData['status'] ?? 'published';
      tripData['trip_type'] = tripData['trip_type'] ?? 'mixed';
      tripData['is_price_negotiable'] =
          tripData['is_price_negotiable'] ?? false;
      tripData['is_instant_booking'] = tripData['is_instant_booking'] ?? false;

      // Handle amenities as JSONB
      if (tripData['amenities'] is List) {
        tripData['amenities'] = tripData['amenities'];
      } else {
        tripData['amenities'] = [];
      }

      if (kDebugMode) {
        print('Creating trip with data: $tripData');
        print('Current user ID: ${user.id}');
        print('Driver ID being set: ${tripData['driver_id']}');
      }

      final response =
          await _client.from('trips').insert(tripData).select().single();

      if (kDebugMode) {
        print('Trip created successfully: ${response['id']}');
        print('Trip driver_id: ${response['driver_id']}');
      }

      return response['id'] as String;
    } catch (e) {
      if (kDebugMode) {
        print('Create trip error: $e');
        print('Error type: ${e.runtimeType}');
      }
      rethrow;
    }
  }

  static Future<List<TripModel>> getTrips({
    String? leaderId,
    String? status,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      var query = _client
          .from('trips')
          .select('*, leader:users!trips_leader_id_fkey(*)');

      if (leaderId != null) {
        // Query for user's own trips using both leader_id and driver_id for compatibility
        query = query.or('leader_id.eq.$leaderId,driver_id.eq.$leaderId');
        // For user's own trips, don't filter by status - show all their trips
      } else {
        // For public trip listing, only show published trips
        if (status != null) {
          query = query.eq('status', status);
        } else {
          query = query.eq('status', 'published');
        }
      }

      final response = await query
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      if (kDebugMode) {
        print(
            'Retrieved ${response.length} trips for leaderId: $leaderId, status: $status');
        if (response.isNotEmpty) {
          print('First trip: ${response.first}');
          print('First trip driver_id: ${response.first['driver_id']}');
          print('First trip leader_id: ${response.first['leader_id']}');
        }
      }

      return response
          .map<TripModel>((json) => TripModel.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Get trips error: $e');
        print('Error type: ${e.runtimeType}');
        print('Stack trace: ${StackTrace.current}');
      }
      return [];
    }
  }

  static Future<TripModel?> getTrip(String tripId) async {
    try {
      final response = await _client
          .from('trips')
          .select('*, leader:users!trips_leader_id_fkey(*)')
          .eq('id', tripId)
          .single();

      if (kDebugMode) {
        print('Retrieved trip: ${response['id']}');
        print('Leader data: ${response['leader']}');
      }

      return TripModel.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('Get trip error: $e');
        print('Error type: ${e.runtimeType}');
      }
      return null;
    }
  }

  // Get distinct city names for autocomplete search
  static Future<List<String>> getDistinctCities({String? searchQuery}) async {
    try {
      // Get distinct from_city and to_city from published trips
      final response = await _client
          .from('trips')
          .select('from_city, to_city')
          .eq('status', 'published');

      if (kDebugMode) {
        print('Retrieved ${response.length} trips for city extraction');
      }

      // Extract unique cities
      final Set<String> cities = {};
      for (final trip in response) {
        if (trip['from_city'] != null &&
            trip['from_city'].toString().trim().isNotEmpty) {
          cities.add(trip['from_city'].toString().trim());
        }
        if (trip['to_city'] != null &&
            trip['to_city'].toString().trim().isNotEmpty) {
          cities.add(trip['to_city'].toString().trim());
        }
      }

      List<String> cityList = cities.toList();

      // Filter by search query if provided
      if (searchQuery != null && searchQuery.trim().isNotEmpty) {
        final query = searchQuery.trim().toLowerCase();
        cityList = cityList
            .where((city) => city.toLowerCase().contains(query))
            .toList();
      }

      // Sort alphabetically
      cityList.sort((a, b) => a.compareTo(b));

      if (kDebugMode) {
        print('Found ${cityList.length} unique cities');
        if (searchQuery != null) {
          print(
              'Filtered by query "$searchQuery": ${cityList.take(5).join(", ")}');
        }
      }

      return cityList;
    } catch (e) {
      if (kDebugMode) {
        print('Get distinct cities error: $e');
      }
      return [];
    }
  }

  // Get user by ID - useful for fetching driver information
  static Future<UserModel?> getUser(String userId) async {
    try {
      final response =
          await _client.from('users').select('*').eq('id', userId).single();

      if (kDebugMode) {
        print('Retrieved user: ${response['id']} - ${response['full_name']}');
      }

      return UserModel.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('Get user error: $e');
        print('Error type: ${e.runtimeType}');
      }
      return null;
    }
  }

  static Future<void> updateTrip(TripModel trip) async {
    try {
      final tripData = trip.toJson();

      // Remove id from update data
      tripData.remove('id');

      // Handle amenities as JSONB
      if (tripData['amenities'] is List) {
        tripData['amenities'] = tripData['amenities'];
      } else {
        tripData['amenities'] = [];
      }

      await _client.from('trips').update(tripData).eq('id', trip.id);

      if (kDebugMode) {
        print('Trip updated successfully: ${trip.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Update trip error: $e');
        print('Error type: ${e.runtimeType}');
      }
      rethrow;
    }
  }

  static Future<void> deleteTrip(String tripId) async {
    try {
      await _client.from('trips').delete().eq('id', tripId);
    } catch (e) {
      if (kDebugMode) {
        print('Delete trip error: $e');
      }
      rethrow;
    }
  }

  // Test function to verify driver_id is being set correctly
  static Future<void> testTripCreation() async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        print('❌ No authenticated user found');
        return;
      }

      print('✅ Current user ID: ${user.id}');

      // Test query to see existing trips
      final existingTrips = await _client
          .from('trips')
          .select('id, driver_id, leader_id, status, from_city, to_city')
          .limit(5);

      print('📊 Sample existing trips:');
      for (var trip in existingTrips) {
        print(
            '  Trip ${trip['id']}: driver_id=${trip['driver_id']}, leader_id=${trip['leader_id']}, status=${trip['status']}');
      }

      // Test user's trips query
      final userTrips = await getTrips(leaderId: user.id);
      print('🚗 User trips found: ${userTrips.length}');

      // Test published trips query
      final publishedTrips = await getTrips(status: 'published');
      print('📢 Published trips found: ${publishedTrips.length}');
    } catch (e) {
      print('❌ Test error: $e');
    }
  }

  // Additional utility methods

  static Future<bool> isHealthy() async {
    try {
      // Simple health check - try to access the database
      await _client.from('users').select('id').limit(1);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Health check failed: $e');
      }
      return false;
    }
  }

  /// Test Supabase connection specifically for web
  static Future<bool> testWebConnection() async {
    try {
      if (kDebugMode) {
        print('Testing Supabase connection...');
      }

      // Test basic connectivity
      final response = await _client.from('users').select('count').limit(1);

      if (kDebugMode) {
        print('Connection test successful: ${response.toString()}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Connection test failed: $e');
        print('Error type: ${e.runtimeType}');
      }
      return false;
    }
  }
}
