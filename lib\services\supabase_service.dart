import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../models/trip_model.dart';

class SupabaseService {
  static final SupabaseClient _client = Supabase.instance.client;

  // Session management keys
  static const String _userIdKey = 'current_user_id';
  static const String _isLoggedInKey = 'is_logged_in';

  // Helper method to normalize phone number
  static String _normalizePhone(String phone) {
    // Remove all non-digit characters
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // Convert to consistent format: remove leading +212 or 0
    String normalizedPhone = cleanPhone;
    if (normalizedPhone.startsWith('212')) {
      normalizedPhone = normalizedPhone.substring(3);
    } else if (normalizedPhone.startsWith('0')) {
      normalizedPhone = normalizedPhone.substring(1);
    }

    return normalizedPhone;
  }

  // Helper method to check if user exists
  static Future<bool> checkUserExists(String phone) async {
    try {
      final normalizedPhone = _normalizePhone(phone);

      final userResponse = await _client
          .from('users')
          .select('id')
          .eq('phone', normalizedPhone)
          .maybeSingle();

      return userResponse != null;
    } catch (e) {
      if (kDebugMode) {
        print('Check user exists error: $e');
      }
      return false;
    }
  }

  // Session management methods
  static Future<void> _saveSession(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, userId);
    await prefs.setBool(_isLoggedInKey, true);
  }

  static Future<void> _clearSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userIdKey);
    await prefs.setBool(_isLoggedInKey, false);
  }

  static Future<String?> getCurrentUserId() async {
    final prefs = await SharedPreferences.getInstance();
    final isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
    if (isLoggedIn) {
      return prefs.getString(_userIdKey);
    }
    return null;
  }

  static Future<bool> isUserLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  // Custom Authentication methods (no Supabase Auth)
  static Future<Map<String, dynamic>> signUpWithPhone({
    required String phone,
    required String password,
    required String fullName,
  }) async {
    try {
      final normalizedPhone = _normalizePhone(phone);

      // Check if user already exists
      final existingUser = await checkUserExists(normalizedPhone);
      if (existingUser) {
        return {
          'success': false,
          'userId': null,
          'message': 'رقم الهاتف مسجل مسبقاً. يرجى تسجيل الدخول',
        };
      }

      // Create user directly in our custom users table
      final userResponse = await _client
          .from('users')
          .insert({
            'phone': normalizedPhone,
            'full_name': fullName,
            'password': password, // Plain text for now
            'role': 'traveler',
          })
          .select()
          .single();

      if (userResponse != null) {
        final userId = userResponse['id'] as String;

        // Save session locally
        await _saveSession(userId);

        if (kDebugMode) {
          print('✅ User created successfully: $userId');
        }

        return {
          'success': true,
          'userId': userId,
          'message': 'تم إنشاء الحساب بنجاح',
        };
      } else {
        return {
          'success': false,
          'userId': null,
          'message': 'فشل في إنشاء الحساب',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Signup error: $e');
      }
      return {
        'success': false,
        'userId': null,
        'message': 'حدث خطأ غير متوقع. حاول مرة أخرى',
      };
    }
  }

  static Future<Map<String, dynamic>> signInWithPhone({
    required String phone,
    required String password,
  }) async {
    try {
      final normalizedPhone = _normalizePhone(phone);

      // Query user directly from our custom users table
      final userResponse = await _client
          .from('users')
          .select('id, phone, password, full_name')
          .eq('phone', normalizedPhone)
          .maybeSingle();

      if (userResponse == null) {
        return {
          'success': false,
          'userId': null,
          'message': 'رقم الهاتف غير مسجل. يرجى إنشاء حساب جديد',
        };
      }

      // Check password (plain text comparison for now)
      if (userResponse['password'] != password) {
        return {
          'success': false,
          'userId': null,
          'message': 'رقم الهاتف أو كلمة المرور غير صحيحة',
        };
      }

      // Login successful - save session
      final userId = userResponse['id'] as String;
      await _saveSession(userId);

      if (kDebugMode) {
        print('✅ Login successful for user: $userId');
      }

      return {
        'success': true,
        'userId': userId,
        'message': 'تم تسجيل الدخول بنجاح',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Signin error: $e');
      }
      return {
        'success': false,
        'userId': null,
        'message': 'حدث خطأ غير متوقع. حاول مرة أخرى',
      };
    }
  }

  static Future<void> signOut() async {
    try {
      // Clear local session
      await _clearSession();

      if (kDebugMode) {
        print('✅ User signed out successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Signout error: $e');
      }
      rethrow;
    }
  }

  static Future<void> resetPassword(String email) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
    } catch (e) {
      if (kDebugMode) {
        print('Reset password error: $e');
      }
      rethrow;
    }
  }

  // User profile methods
  static Future<void> _createUserProfile({
    required String userId,
    required String phone,
    required String fullName,
    required String email,
  }) async {
    try {
      await _client.from('users').insert({
        'id': userId,
        'email': email,
        'full_name': fullName,
        'phone': phone,
        'role': 'traveler',
        'is_leader': false,
        'balance': 0.0,
        'is_verified': false,
        'rating': 0.0,
        'total_trips': 0,
        'total_ratings': 0,
        'badges': [],
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Create user profile error: $e');
      }
      rethrow;
    }
  }

  static Future<UserModel?> getUserProfile(String userId) async {
    try {
      final response =
          await _client.from('users').select().eq('id', userId).single();

      return UserModel.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('Get user profile error: $e');
      }
      return null;
    }
  }

  static Future<void> updateUserProfile(UserModel user) async {
    try {
      await _client.from('users').update(user.toJson()).eq('id', user.id);
    } catch (e) {
      if (kDebugMode) {
        print('Update user profile error: $e');
      }
      rethrow;
    }
  }

  // Trip methods
  static Future<String> createTrip(TripModel trip) async {
    try {
      // Get current authenticated user
      final user = _client.auth.currentUser;
      if (user == null) {
        throw Exception(
            'User not authenticated. Please log in to create a trip.');
      }

      // Prepare trip data for insertion
      final tripData = trip.toJson();

      // Remove null id for insertion
      tripData.remove('id');

      // CRITICAL FIX: Ensure driver_id is set to current user's ID
      tripData['driver_id'] = user.id;
      tripData['leader_id'] = user.id; // Keep both for compatibility

      // Ensure required fields have default values
      tripData['status'] = tripData['status'] ?? 'published';
      tripData['trip_type'] = tripData['trip_type'] ?? 'mixed';
      tripData['is_price_negotiable'] =
          tripData['is_price_negotiable'] ?? false;
      tripData['is_instant_booking'] = tripData['is_instant_booking'] ?? false;

      // Handle amenities as JSONB
      if (tripData['amenities'] is List) {
        tripData['amenities'] = tripData['amenities'];
      } else {
        tripData['amenities'] = [];
      }

      if (kDebugMode) {
        print('Creating trip with data: $tripData');
        print('Current user ID: ${user.id}');
        print('Driver ID being set: ${tripData['driver_id']}');
      }

      final response =
          await _client.from('trips').insert(tripData).select().single();

      if (kDebugMode) {
        print('Trip created successfully: ${response['id']}');
        print('Trip driver_id: ${response['driver_id']}');
      }

      return response['id'] as String;
    } catch (e) {
      if (kDebugMode) {
        print('Create trip error: $e');
        print('Error type: ${e.runtimeType}');
      }
      rethrow;
    }
  }

  static Future<List<TripModel>> getTrips({
    String? leaderId,
    String? status,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      var query = _client
          .from('trips')
          .select('*, leader:users!trips_leader_id_fkey(*)');

      if (leaderId != null) {
        // Query for user's own trips using both leader_id and driver_id for compatibility
        query = query.or('leader_id.eq.$leaderId,driver_id.eq.$leaderId');
        // For user's own trips, don't filter by status - show all their trips
      } else {
        // For public trip listing, only show published trips
        if (status != null) {
          query = query.eq('status', status);
        } else {
          query = query.eq('status', 'published');
        }
      }

      final response = await query
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      if (kDebugMode) {
        print(
            'Retrieved ${response.length} trips for leaderId: $leaderId, status: $status');
        if (response.isNotEmpty) {
          print('First trip: ${response.first}');
          print('First trip driver_id: ${response.first['driver_id']}');
          print('First trip leader_id: ${response.first['leader_id']}');
        }
      }

      return response
          .map<TripModel>((json) => TripModel.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Get trips error: $e');
        print('Error type: ${e.runtimeType}');
        print('Stack trace: ${StackTrace.current}');
      }
      return [];
    }
  }

  static Future<TripModel?> getTrip(String tripId) async {
    try {
      final response = await _client
          .from('trips')
          .select('*, leader:users!trips_leader_id_fkey(*)')
          .eq('id', tripId)
          .single();

      if (kDebugMode) {
        print('Retrieved trip: ${response['id']}');
        print('Leader data: ${response['leader']}');
      }

      return TripModel.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('Get trip error: $e');
        print('Error type: ${e.runtimeType}');
      }
      return null;
    }
  }

  // Get distinct city names for autocomplete search
  static Future<List<String>> getDistinctCities({String? searchQuery}) async {
    try {
      // Get distinct from_city and to_city from published trips
      final response = await _client
          .from('trips')
          .select('from_city, to_city')
          .eq('status', 'published');

      if (kDebugMode) {
        print('Retrieved ${response.length} trips for city extraction');
      }

      // Extract unique cities
      final Set<String> cities = {};
      for (final trip in response) {
        if (trip['from_city'] != null &&
            trip['from_city'].toString().trim().isNotEmpty) {
          cities.add(trip['from_city'].toString().trim());
        }
        if (trip['to_city'] != null &&
            trip['to_city'].toString().trim().isNotEmpty) {
          cities.add(trip['to_city'].toString().trim());
        }
      }

      List<String> cityList = cities.toList();

      // Filter by search query if provided
      if (searchQuery != null && searchQuery.trim().isNotEmpty) {
        final query = searchQuery.trim().toLowerCase();
        cityList = cityList
            .where((city) => city.toLowerCase().contains(query))
            .toList();
      }

      // Sort alphabetically
      cityList.sort((a, b) => a.compareTo(b));

      if (kDebugMode) {
        print('Found ${cityList.length} unique cities');
        if (searchQuery != null) {
          print(
              'Filtered by query "$searchQuery": ${cityList.take(5).join(", ")}');
        }
      }

      return cityList;
    } catch (e) {
      if (kDebugMode) {
        print('Get distinct cities error: $e');
      }
      return [];
    }
  }

  // Get user by ID - useful for fetching driver information
  static Future<UserModel?> getUser(String userId) async {
    try {
      final response =
          await _client.from('users').select('*').eq('id', userId).single();

      if (kDebugMode) {
        print('Retrieved user: ${response['id']} - ${response['full_name']}');
      }

      return UserModel.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('Get user error: $e');
        print('Error type: ${e.runtimeType}');
      }
      return null;
    }
  }

  static Future<void> updateTrip(TripModel trip) async {
    try {
      final tripData = trip.toJson();

      // Remove id from update data
      tripData.remove('id');

      // Handle amenities as JSONB
      if (tripData['amenities'] is List) {
        tripData['amenities'] = tripData['amenities'];
      } else {
        tripData['amenities'] = [];
      }

      await _client.from('trips').update(tripData).eq('id', trip.id);

      if (kDebugMode) {
        print('Trip updated successfully: ${trip.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Update trip error: $e');
        print('Error type: ${e.runtimeType}');
      }
      rethrow;
    }
  }

  static Future<void> deleteTrip(String tripId) async {
    try {
      await _client.from('trips').delete().eq('id', tripId);
    } catch (e) {
      if (kDebugMode) {
        print('Delete trip error: $e');
      }
      rethrow;
    }
  }

  // Test function to verify driver_id is being set correctly
  static Future<void> testTripCreation() async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) {
        print('❌ No authenticated user found');
        return;
      }

      print('✅ Current user ID: ${user.id}');

      // Test query to see existing trips
      final existingTrips = await _client
          .from('trips')
          .select('id, driver_id, leader_id, status, from_city, to_city')
          .limit(5);

      print('📊 Sample existing trips:');
      for (var trip in existingTrips) {
        print(
            '  Trip ${trip['id']}: driver_id=${trip['driver_id']}, leader_id=${trip['leader_id']}, status=${trip['status']}');
      }

      // Test user's trips query
      final userTrips = await getTrips(leaderId: user.id);
      print('🚗 User trips found: ${userTrips.length}');

      // Test published trips query
      final publishedTrips = await getTrips(status: 'published');
      print('📢 Published trips found: ${publishedTrips.length}');
    } catch (e) {
      print('❌ Test error: $e');
    }
  }

  // Utility methods
  static User? get currentUser => _client.auth.currentUser;

  static Stream<AuthState> get authStateChanges =>
      _client.auth.onAuthStateChange;

  static Future<bool> isHealthy() async {
    try {
      // Simple health check - try to access the database
      await _client.from('users').select('id').limit(1);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Health check failed: $e');
      }
      return false;
    }
  }

  /// Test Supabase connection specifically for web
  static Future<bool> testWebConnection() async {
    try {
      if (kDebugMode) {
        print('Testing Supabase connection...');
      }

      // Test basic connectivity
      final response = await _client.from('users').select('count').limit(1);

      if (kDebugMode) {
        print('Connection test successful: ${response.toString()}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Connection test failed: $e');
        print('Error type: ${e.runtimeType}');
      }
      return false;
    }
  }
}
